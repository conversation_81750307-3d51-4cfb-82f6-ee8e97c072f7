@page "/students"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@attribute [RequireLevel80Access]
@inherits StudentsBase

<PageTitle>Student Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">👨‍🎓 <span class="d-none d-sm-inline">Student Management</span><span class="d-sm-none">Students</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading students...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Students</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateModal">
                                    <i class="bi bi-person-add" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Student</span>
                                    <span class="d-sm-none ms-2">Add Student</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@students" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="900" CssClass="mobile-grid">
                            <GridPageSettings PageSize="20"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(Student.CreatedUTC)" Direction="SortDirection.Descending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Student.StudentId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Student.StudentName) HeaderText="Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Student.Email) HeaderText="Email" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(Student.TutorID) HeaderText="Tutor" Width="150">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var tutor = tutors.FirstOrDefault(t => t.TutorId == student?.TutorID);
                                        }
                                        <span>@(tutor?.TutorName ?? "No Tutor")</span>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Student.SubjectId) HeaderText="Subject" Width="100">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var subject = subjects.FirstOrDefault(s => s.SubjectId == student?.SubjectId);
                                        }
                                        <span>@(subject?.SubjectName ?? "No Subject")</span>
                                    </Template>
                                </GridColumn>
                                @* <GridColumn Field=@nameof(Student.LoginName) HeaderText="Login Name" Width="100"></GridColumn> *@
                                <GridColumn HeaderText="Remaining Lessons" Width="120" AllowFiltering="false" AllowSorting="true">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var remainingLessons = GetRemainingLessons(student?.StudentId ?? 0);
                                        }
                                        <span class="remaining-lessons-badge @(remainingLessons <= 3 ? "remaining-lessons-low" : "remaining-lessons-normal")">
                                            @remainingLessons
                                        </span>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Student.ExcludeEmail) HeaderText="Email Reminders" Width="100" AllowFiltering="false">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                        }
                                        <span class="email-reminder-badge @(student?.ExcludeEmail == true ? "email-reminder-disabled" : "email-reminder-enabled")">
                                            @(student?.ExcludeEmail == true ? "Disabled" : "Enabled")
                                        </span>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Student.CreatedUTC) HeaderText="Created" Width="100" Format="dd/MM/yyyy"
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-success btn-sm grid-action-btn grid-btn-third" @onclick="() => ViewLessons(student)"
                                                    title="View Lessons">
                                                <i class="bi bi-calendar-check" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Lessons</span>
                                                }
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-third" @onclick="() => OpenEditModal(student)"
                                                    title="Edit Student">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Edit</span>
                                                }
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm grid-action-btn grid-btn-third" @onclick="() => DeleteStudent(student)"
                                                    title="Delete Student">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Delete</span>
                                                }
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentStudent" OnValidSubmit="@SaveStudent">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Student Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentStudent.StudentName" Placeholder="Enter student name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.StudentName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentStudent.Email" Placeholder="Enter email address"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.Email)" />
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" @bind="currentStudent.ExcludeEmail" id="excludeEmailCheck">
                        <label class="form-check-label" for="excludeEmailCheck">
                            <i class="bi bi-envelope-slash me-2"></i>Exclude from payment reminder emails
                        </label>
                    </div>
                    <small class="form-text text-muted">
                        When checked, this student will not receive automated payment reminder emails when they have few lessons remaining.
                    </small>
                </div>

                <div class="mb-3">
                    <label class="form-label">Subject @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentSubjectName" Readonly="true"
                                   CssClass="form-control" Placeholder="No subject assigned"></SfTextBox>
                        <small class="form-text text-muted">Subject cannot be changed when editing. Create a new student to assign a different subject.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Subject" @bind-Value="currentStudent.SubjectId"
                                        DataSource="@subjects" CssClass="form-control" Placeholder="Select a subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Subject">
                                <ItemTemplate Context="subjectItem">
                                    <span>@((subjectItem as Subject)?.SubjectName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a subject for this student (required)</small>
                        @if (showSubjectValidation)
                        {
                            <div class="text-danger">Subject is required.</div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Assigned Tutor @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentTutorName" Readonly="true"
                                   CssClass="form-control" Placeholder="No tutor assigned"></SfTextBox>
                        <small class="form-text text-muted">Tutor cannot be changed when editing. Create a new student to assign a different tutor.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="currentStudent.TutorID"
                                        DataSource="@tutors" CssClass="form-control" Placeholder="Select a tutor">
                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Tutor">
                                <ItemTemplate Context="tutorItem">
                                    <span>@((tutorItem as Tutor)?.TutorName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a tutor to assign to this student (required)</small>
                        @if (showTutorValidation)
                        {
                            <div class="text-danger">Tutor is required.</div>
                        }
                    }
                </div>
                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentStudent.LoginName" Placeholder="Assigned via user management"
                               CssClass="form-control" Readonly="true"></SfTextBox>
                    <small class="form-text text-muted">Login name is managed through the Admin page user assignment</small>
                </div>
                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />
