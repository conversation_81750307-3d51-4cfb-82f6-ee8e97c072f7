namespace ShiningCMusicCommon.Models
{
    public class StudentWithRemainingLessons
    {
        public int StudentId { get; set; }
        public string? StudentName { get; set; }
        public string? Email { get; set; }
        public DateTime CreatedUTC { get; set; }
        public DateTime? UpdatedUTC { get; set; }
        public bool IsArchived { get; set; }
        public bool ExcludeEmail { get; set; }
        public int? TutorID { get; set; }
        public int? SubjectId { get; set; }
        public string? LoginName { get; set; }
        public int RemainingLessons { get; set; }

        // Navigation properties
        public virtual Tutor? Tutor { get; set; }
        public virtual Subject? Subject { get; set; }
        public virtual User? User { get; set; }
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();

        // Constructor to create from Student
        public StudentWithRemainingLessons() { }

        public StudentWithRemainingLessons(Student student, int remainingLessons)
        {
            StudentId = student.StudentId;
            StudentName = student.StudentName;
            Email = student.Email;
            CreatedUTC = student.CreatedUTC;
            UpdatedUTC = student.UpdatedUTC;
            IsArchived = student.IsArchived;
            ExcludeEmail = student.ExcludeEmail;
            TutorID = student.TutorID;
            SubjectId = student.SubjectId;
            LoginName = student.LoginName;
            RemainingLessons = remainingLessons;
            Tutor = student.Tutor;
            Subject = student.Subject;
            User = student.User;
            Lessons = student.Lessons;
        }
    }
}
